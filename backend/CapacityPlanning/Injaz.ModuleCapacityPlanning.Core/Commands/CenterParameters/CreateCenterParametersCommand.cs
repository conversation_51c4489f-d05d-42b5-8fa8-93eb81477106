using System.ComponentModel.DataAnnotations;
using Injaz.Core.Dtos.CapacityPlanning.CenterParameters;
using Injaz.Core.Dtos.Department;
using MediatR;

namespace Injaz.ModuleCapacityPlanning.Core.Commands.CenterParameters;

public class CreateCenterParametersCommand : IRequest<CenterParametersGetDto>
{
    [Required(ErrorMessage = "0_is_required")]
    public DepartmentSimpleDto? Department { get; set; }

    [Required(ErrorMessage = "0_is_required")]
    public int Year { get; set; }

    public string? Remarks { get; set; }


    // Station Information
    public int AvailableStationsCount { get; set; }
    public int AvailableParkingSpaces { get; set; }

    public double TotalOfficialWorkingHoursForStation { get; set; }
    public double TotalStationAvailabilityHoursPerDay { get; set; }


    // Employee Parameters
    public double SickLeaveDaysPerEmployeeYearly { get; set; }
    public double TrainingDaysPerEmployeeYearly { get; set; }
    public double OfficialWorkDaysPerEmployeeYearly { get; set; }
    public double PersonalPermissionDaysYearly { get; set; }

    public int ReceptionEmployeesCount { get; set; }
    public double AverageEmployeeActualHourlyProductivity { get; set; }

    public double AnnualEmployeeLeaves { get; set; }

    public double TotalActualEmployeeWorkingHoursPerDay { get; set; }

    // Service Parameters
    public double FastServiceTargetTime { get; set; }
    public int FastServicesCount { get; set; }
    public double NormalServiceTargetTime { get; set; }
    public int NormalServicesCount { get; set; }
    public double ComplexServiceTargetTime { get; set; }
    public int ComplexServicesCount { get; set; }
    public int TotalCenterServices { get; set; }
    public double AverageTargetTimeAllServices { get; set; }

    public int TotalPeakHours { get; set; }
    public double TotalStationWorkingDaysPerMonth { get; set; }

}
