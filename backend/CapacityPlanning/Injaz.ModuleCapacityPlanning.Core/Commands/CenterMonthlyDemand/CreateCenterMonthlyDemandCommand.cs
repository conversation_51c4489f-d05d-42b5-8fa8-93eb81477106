using System.ComponentModel.DataAnnotations;
using Injaz.Core.Dtos.CapacityPlanning.CenterMonthlyDemand;
using Injaz.Core.Dtos.CapacityPlanning.TimeDimension;
using Injaz.Core.Dtos.Department;
using MediatR;

namespace Injaz.ModuleCapacityPlanning.Core.Commands.CenterMonthlyDemand;

public class CreateCenterMonthlyDemandCommand : IRequest<CenterMonthlyDemandGetDto>
{
    [Required(ErrorMessage = "0_is_required")]
    public DepartmentSimpleDto Department { get; set; } = null!;

    [Required(ErrorMessage = "0_is_required")]
    public TimeDimensionSimpleDto Time { get; set; } = null!;

    // Employee data
    public int EmployeesAvailable { get; set; }

    // Service data
    public int FastServicesCount { get; set; }
    public double FastServiceTime { get; set; }
    public int RegularServicesCount { get; set; }
    public double RegularServiceTime { get; set; }
    public int ComplexServicesCount { get; set; }
    public double ComplexServiceTime { get; set; }

    // Time parameters
    public float DailyWorkHoursPerEmployee { get; set; }
    public float WorkDaysPerMonth { get; set; }
    public float WorkHoursPerDay { get; set; }
    public float ReligiousAndNationalOccasionsDaysPerMonth { get; set; }

    // Actual usage data
    public float ActualTransactionsPerMonth { get; set; }
    public float ActualCustomersPerMonth { get; set; }

    // Facilities data
    public int AvailableSeatsInCenter { get; set; }
    public int AvailableParkingAtCenter { get; set; }

    // related tot the services channels
    public int HappinessCenterVolume { get; set; }
    public int MoiAppVolume { get; set; }
    public int AjpAppVolume { get; set; }
    public int WebsiteVolume { get; set; }
}
