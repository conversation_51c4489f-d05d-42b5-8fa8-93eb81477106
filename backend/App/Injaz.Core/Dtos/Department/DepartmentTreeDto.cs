using System;
using System.Collections.Generic;
using System.Linq.Expressions;

public class DepartmentTreeDto
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public string FullPath { get; set; }
    public int LevelNumber { get; set; }
    public int Order { get; set; }
    public bool IsMain { get; set; }
    public bool IsPoliceStation { get; set; }
    public Guid? ParentDepartmentId { get; set; }
    public List<DepartmentTreeDto> Children { get; set; } = new List<DepartmentTreeDto>();

    public static Expression<Func<Injaz.Core.Models.DomainClasses.App.Department, DepartmentTreeDto>> Mapper(string lang)
    {
        return item => new DepartmentTreeDto
        {
            Id = item.Id,
            Name = lang == "en" ? item.NameEn : item.NameAr,
            LevelNumber = item.LevelNumber ?? 1,
            Order = item.Order,
            IsMain = item.IsMain == 1,
            IsPoliceStation = item.IsPoliceStation,
            ParentDepartmentId = item.ParentDepartmentId,
            FullPath = string.Empty
        };
    }
}