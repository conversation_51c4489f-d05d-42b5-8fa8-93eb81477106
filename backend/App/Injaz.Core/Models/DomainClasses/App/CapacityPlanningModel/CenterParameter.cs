using System;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App.CapacityPlanningModel;

[Table("capacity_center_parameters")]
public class CenterParameter : ModifiableModel<User>
{
    [Column("year")]
    public int Year { get; set; }

    // Station Information
    [Column("available_stations_count")]
    public int AvailableStationsCount { get; set; }

    [Column("station_utilization_percentage_computed")]
    public double StationUtilizationPercentageComputed { get; set; }

    [Column("ideal_station_hourly_productivity_computed")]
    public double IdealStationHourlyProductivityComputed { get; set; }

    [Column("available_seats_count")]
    public int AvailableSeatsCount { get; set; }

    [Column("available_parking_spaces")]
    public int AvailableParkingSpaces { get; set; }

    [Column("total_station_availability_days_per_year_computed")]
    public int TotalStationAvailabilityDaysPerYearComputed { get; set; }

    [Column("total_official_working_hours_for_station")]
    public double TotalOfficialWorkingHoursForStation { get; set; }

    [Column("total_station_availability_hours_per_day")]
    public double TotalStationAvailabilityHoursPerDay { get; set; }

    [Column("total_station_shifts_computed")]
    public int TotalStationShiftsComputed { get; set; }


    // Employee Parameters
    [Column("sick_leave_days_per_employee_yearly")]
    public double SickLeaveDaysPerEmployeeYearly { get; set; }

    [Column("training_days_per_employee_yearly")]
    public double TrainingDaysPerEmployeeYearly { get; set; }

    [Column("official_work_days_per_employee_yearly")]
    public double OfficialWorkDaysPerEmployeeYearly { get; set; }

    [Column("personal_permission_days_yearly")]
    public double PersonalPermissionDaysYearly { get; set; }

    [Column("effective_work_days_yearly_computed")]
    public double EffectiveWorkDaysYearlyComputed { get; set; }

    [Column("work_hours_per_employee_yearly_computed")]
    public double WorkHoursPerEmployeeYearlyComputed { get; set; }

    [Column("available_minutes_per_employee_yearly_computed")]
    public double AvailableMinutesPerEmployeeYearlyComputed { get; set; }

    [Column("available_minutes_per_employee_monthly_computed")]
    public double AvailableMinutesPerEmployeeMonthlyComputed { get; set; }

    [Column("reception_employees_count")]
    public int ReceptionEmployeesCount { get; set; }

    [Column("average_employee_actual_hourly_productivity")]
    public double AverageEmployeeActualHourlyProductivity { get; set; }

    [Column("employee_efficiency_percentage_computed")]
    public double EmployeeEfficiencyPercentageComputed { get; set; }

    [Column("annual_employee_leaves")]
    public double AnnualEmployeeLeaves { get; set; }

    [Column("daily_working_minutes_computed")]
    public double DailyWorkingMinutesComputed { get; set; }

    [Column("total_actual_employee_working_hours_per_day")]
    public double TotalActualEmployeeWorkingHoursPerDay { get; set; }


    // Service Parameters
    [Column("fast_service_target_time")]
    public double FastServiceTargetTime { get; set; }

    [Column("normal_service_target_time")]
    public double NormalServiceTargetTime { get; set; }

    [Column("fast_services_count")]
    public int FastServicesCount { get; set; }

    [Column("normal_services_count")]
    public int NormalServicesCount { get; set; }

    [Column("complex_service_target_time")]
    public double ComplexServiceTargetTime { get; set; }

    [Column("complex_services_count")]
    public int ComplexServicesCount { get; set; }

    [Column("total_center_services")]
    public int TotalCenterServices { get; set; }

    [Column("average_target_time_all_services")]
    public double AverageTargetTimeAllServices { get; set; }

    [Column("total_station_working_days_per_month")]
    public double TotalStationWorkingDaysPerMonth { get; set; }

    [Column("total_religious_and_national_event_days_per_year_computed")]
    public double TotalReligiousAndNationalEventDaysPerYearComputed { get; set; }

    [Column("total_peak_hours")]
    public double TotalPeakHours { get; set; }

    [Column("department_id")]
    public Guid DepartmentId { get; set; }

    [ForeignKey(nameof(DepartmentId))]
    [InverseProperty(nameof(App.Department.CenterParameters))]
    public virtual Department Department { get; set; }
}
