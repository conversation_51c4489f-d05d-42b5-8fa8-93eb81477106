using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Injaz.Core.Models.DomainClasses.App.CapacityPlanningModel;
using Injaz.Core.Models.DomainClasses.App.PartnershipModel;
using Injaz.Core.Models.DomainClasses.App.RiskModel;
using Injaz.Core.Models.DomainClasses.App.ServiceModel;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("departments")]
public class Department : ModifiableModel<User>
{
    [Column("name_ar")] [MaxLength(1024)] public string NameAr { get; set; }

    [Column("name_en")] [MaxLength(1024)] public string NameEn { get; set; }

    [Column("specializations")] public string Specializations { get; set; }

    [Column("hierarchy_code")]
    [MaxLength(1024)]
    public string HierarchyCode { get; set; } // 000

    // Manager information
    [Column("manager_name")]
    [MaxLength(256)]
    public string ManagerName { get; set; }

    [Column("manager_title")]
    [MaxLength(256)]
    public string MangerTitle { get; set; }

    [Column("manager_staff_number")]
    [MaxLength(256)]
    public string ManagerStaffNumber { get; set; }

    [Column("manager_email")]
    [MaxLength(8192)]
    public string ManagerEmail { get; set; }

    // Old IDs & orders
    [Column("org_id")] [MaxLength(256)] public string OldOrgId { get; set; }

    [Column("is_main")] public int IsMain { get; set; }

    [Column("organization_type_id")] public Guid? OrganizationTypeId { get; set; }

    [Column("order")] public int Order { get; set; }

    [Column("level_number")] public int? LevelNumber { get; set; }

    [Column("is_police_station")] public bool IsPoliceStation { get; set; }

    [Column("parent_department_id")] public Guid? ParentDepartmentId { get; set; }

    // [Column("hierarchy")]
    // public string Hierarchy { get; set; }

    [ForeignKey("ParentDepartmentId")]
    [InverseProperty("Children")]
    public Department ParentDepartment { get; set; }

    [ForeignKey("OrganizationTypeId")] public OrganizationType OrganizationType { get; set; }

    public virtual ICollection<DepartmentUserLink> UserLinks { get; set; }
    public virtual ICollection<ServiceDepartmentLink> ServiceLinks { get; set; }

    // Cascade deletion
    public ICollection<DepartmentKpiLink> KpiLinks { get; set; }
    public ICollection<PlanPartneringDepartmentLink> PlanLinks { get; set; }
    // Restrict deletion
    public ICollection<Department> Children { get; set; }
    public ICollection<Operation> Operations { get; set; }
    public ICollection<KpiResult> KpiResults { get; set; }
    public ICollection<Kpi> OwnedKpis { get; set; }
    public ICollection<Kpi> MeasuredKpis { get; set; }
    public ICollection<Plan> Plans { get; set; }
    public ICollection<Benchmark> Benchmarks { get; set; }

    public ICollection<PlanTask> Tasks { get; set; }
    public ICollection<PlanSubtask> Subtasks { get; set; }

    public ICollection<ImprovementOpportunity> OwnedOpportunities { get; set; }
    public ICollection<Service> OwnedServices { get; set; }
    public ICollection<StatisticalReportCategoryDepartmentLink> StatisticalReportCategoryLinks { get; set; }

    public ICollection<PartnershipContract> PartnershipContracts { get; set; }

    // Capacity Planning
    public ICollection<ServiceChannelDemand> ServiceChannelDemands { get; set; }

    public ICollection<ServiceMonthlyDemand> ServiceMonthlyDemands { get; set; }

    public ICollection<TimeDimensionEntryPeriod> DataEntryPeriods { get; set; }

    public ICollection<CenterParameter> CenterParameters { get; set; }

    public ICollection<CenterMonthlyDemand> CenterMonthlyDemands { get; set; }

    public ICollection<CenterServiceLink> CenterServiceLinks { get; set; }

    public ICollection<Risk> Risks { get; set; }

    public const int HierarchyCodeLength = 3;
}
