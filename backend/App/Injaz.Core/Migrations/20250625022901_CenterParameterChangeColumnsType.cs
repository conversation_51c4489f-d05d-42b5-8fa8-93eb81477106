﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Injaz.Core.Migrations
{
    public partial class CenterParameterChangeColumnsType : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<double>(
                name: "training_days_per_employee_yearly",
                table: "capacity_center_parameters",
                type: "float",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<double>(
                name: "total_station_working_days_per_month",
                table: "capacity_center_parameters",
                type: "float",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<double>(
                name: "total_religious_and_national_event_days_per_year_computed",
                table: "capacity_center_parameters",
                type: "float",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<double>(
                name: "total_peak_hours",
                table: "capacity_center_parameters",
                type: "float",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<double>(
                name: "total_official_working_hours_for_station",
                table: "capacity_center_parameters",
                type: "float",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<double>(
                name: "total_actual_employee_working_hours_per_day",
                table: "capacity_center_parameters",
                type: "float",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<double>(
                name: "sick_leave_days_per_employee_yearly",
                table: "capacity_center_parameters",
                type: "float",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<double>(
                name: "personal_permission_days_yearly",
                table: "capacity_center_parameters",
                type: "float",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<double>(
                name: "official_work_days_per_employee_yearly",
                table: "capacity_center_parameters",
                type: "float",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<double>(
                name: "effective_work_days_yearly_computed",
                table: "capacity_center_parameters",
                type: "float",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<double>(
                name: "daily_working_minutes_computed",
                table: "capacity_center_parameters",
                type: "float",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<double>(
                name: "annual_employee_leaves",
                table: "capacity_center_parameters",
                type: "float",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "training_days_per_employee_yearly",
                table: "capacity_center_parameters",
                type: "int",
                nullable: false,
                oldClrType: typeof(double),
                oldType: "float");

            migrationBuilder.AlterColumn<int>(
                name: "total_station_working_days_per_month",
                table: "capacity_center_parameters",
                type: "int",
                nullable: false,
                oldClrType: typeof(double),
                oldType: "float");

            migrationBuilder.AlterColumn<int>(
                name: "total_religious_and_national_event_days_per_year_computed",
                table: "capacity_center_parameters",
                type: "int",
                nullable: false,
                oldClrType: typeof(double),
                oldType: "float");

            migrationBuilder.AlterColumn<int>(
                name: "total_peak_hours",
                table: "capacity_center_parameters",
                type: "int",
                nullable: false,
                oldClrType: typeof(double),
                oldType: "float");

            migrationBuilder.AlterColumn<int>(
                name: "total_official_working_hours_for_station",
                table: "capacity_center_parameters",
                type: "int",
                nullable: false,
                oldClrType: typeof(double),
                oldType: "float");

            migrationBuilder.AlterColumn<int>(
                name: "total_actual_employee_working_hours_per_day",
                table: "capacity_center_parameters",
                type: "int",
                nullable: false,
                oldClrType: typeof(double),
                oldType: "float");

            migrationBuilder.AlterColumn<int>(
                name: "sick_leave_days_per_employee_yearly",
                table: "capacity_center_parameters",
                type: "int",
                nullable: false,
                oldClrType: typeof(double),
                oldType: "float");

            migrationBuilder.AlterColumn<int>(
                name: "personal_permission_days_yearly",
                table: "capacity_center_parameters",
                type: "int",
                nullable: false,
                oldClrType: typeof(double),
                oldType: "float");

            migrationBuilder.AlterColumn<int>(
                name: "official_work_days_per_employee_yearly",
                table: "capacity_center_parameters",
                type: "int",
                nullable: false,
                oldClrType: typeof(double),
                oldType: "float");

            migrationBuilder.AlterColumn<int>(
                name: "effective_work_days_yearly_computed",
                table: "capacity_center_parameters",
                type: "int",
                nullable: false,
                oldClrType: typeof(double),
                oldType: "float");

            migrationBuilder.AlterColumn<int>(
                name: "daily_working_minutes_computed",
                table: "capacity_center_parameters",
                type: "int",
                nullable: false,
                oldClrType: typeof(double),
                oldType: "float");

            migrationBuilder.AlterColumn<int>(
                name: "annual_employee_leaves",
                table: "capacity_center_parameters",
                type: "int",
                nullable: false,
                oldClrType: typeof(double),
                oldType: "float");
        }
    }
}
