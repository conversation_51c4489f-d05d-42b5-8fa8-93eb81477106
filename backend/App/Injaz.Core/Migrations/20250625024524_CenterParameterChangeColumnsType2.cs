﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Injaz.Core.Migrations
{
    public partial class CenterParameterChangeColumnsType2 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<double>(
                name: "total_station_availability_hours_per_day",
                table: "capacity_center_parameters",
                type: "float",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "total_station_availability_hours_per_day",
                table: "capacity_center_parameters",
                type: "int",
                nullable: false,
                oldClrType: typeof(double),
                oldType: "float");
        }
    }
}
