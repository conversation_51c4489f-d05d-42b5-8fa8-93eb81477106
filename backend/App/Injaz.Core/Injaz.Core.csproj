<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <Version>0.0.0</Version>
        <TargetFramework>net6.0</TargetFramework>
        <LangVersion>10</LangVersion>
        <ImplicitUsings>disable</ImplicitUsings>
        <NoWarn>AD0001</NoWarn>
        <DisableImplicitNamespaceImports>true</DisableImplicitNamespaceImports>
    </PropertyGroup>

    <ItemGroup>
        <FrameworkReference Include="Microsoft.AspNetCore.App" />
        <PackageReference Include="DotnetOmar.Translation" Version="1.0.9" />
        <PackageReference Include="Humanizer.Core" Version="2.14.1" />
        <PackageReference Include="MediatR" Version="11.1.0" />
        <PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="11.0.0" />
        <PackageReference Include="MNMWebApp" Version="4.0.7" />
        <PackageReference Include="MNMWebApp.OAuth" Version="1.2.2" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.0" />
        <PackageReference Include="Novell.Directory.Ldap.NETStandard" Version="3.6.0" />
        <PackageReference Include="SixLabors.ImageSharp" Version="3.1.7" />
        <PackageReference Include="SixLabors.ImageSharp.Drawing" Version="2.0.0" />
        <PackageReference Include="LinqKit" Version="1.1.24" />
        <PackageReference Include="nClam" Version="4.0.1" />
        <PackageReference Include="Minio" Version="3.1.13" />
        <PackageReference Include="Redi.StackExchangeRedis.Cache.Extension" Version="1.5.3" />
        <PackageReference Include="System.Linq.Dynamic.Core" Version="1.6.0.2" />
        <PackageReference Include="EPPlus" Version="5.6.1" />
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Evaluate\Cqrs\" />
    </ItemGroup>

    <ItemGroup>
      <Content Update="global.json">
        <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </Content>
    </ItemGroup>


<!--        <PropertyGroup>-->
<!--            <DefaultItemExcludes>$(DefaultItemExcludes);Migrations\**</DefaultItemExcludes>-->
<!--        </PropertyGroup>-->
</Project>
