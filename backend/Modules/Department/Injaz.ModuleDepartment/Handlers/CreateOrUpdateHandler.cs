using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Exceptions;
using Injaz.Core.Extensions;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Resources.Shared;
using Injaz.Core.Services;
using Injaz.ModuleDepartment.Core.Commands;
using MediatR;
using Microsoft.Extensions.Localization;

namespace Injaz.ModuleDepartment.Handlers;

public class CreateOrUpdateHandler :
    IRequestHandler<CreateDepartmentCommand, DepartmentGetDto>,
    IRequestHandler<UpdateDepartmentCommand, DepartmentGetDto>
{
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly DbContext _db;
    private readonly ValidationService _validationService;

    public CreateOrUpdateHandler(
        IStringLocalizer<SharedResource> localizer,
        DbContext db,
        ValidationService validationService
    )
    {
        _localizer = localizer;
        _db = db;
        _validationService = validationService;
    }

    public Task<DepartmentGetDto> Handle(CreateDepartmentCommand command, CancellationToken cancellationToken)
    {
        return CreateOrUpdate(command);
    }

    public Task<DepartmentGetDto> Handle(UpdateDepartmentCommand command, CancellationToken cancellationToken)
    {
        return CreateOrUpdate(command);
    }

    private Task<DepartmentGetDto> CreateOrUpdate(CreateDepartmentCommand command)
    {
        if (!_validationService.IsValid(command, out var errors))
        {
            throw new GenericException
            {
                Messages = errors.Select(x => x.ErrorMessage).ToArray()
            };
        }


        Department item;

        if (command is UpdateDepartmentCommand updateCommand)
        {
            item = _db.Departments.Find(updateCommand.Id);

            if (item == null)
            {
                throw new ItemNotFoundException();
            }

            // update the hierarchy
            if (item.ParentDepartmentId != command.ParentDepartment?.Id)
            {
                // Ensure that the new parent is not a child (immediate or deep)
                // of the current item (otherwise we're gonna have a cyclic issue).
                if (command.ParentDepartment != null)
                {
                    var newParent = _db.Departments.Find(command.ParentDepartment.Id);

                    if (newParent == null)
                    {
                        throw new GenericException
                        {
                            Messages = new[] { _localizer["could_not_find_the_new_department"].Value }
                        };
                    }

                    if (newParent.HierarchyCode.StartsWith(item.HierarchyCode))
                    {
                        throw new GenericException
                        {
                            Messages = new[] { _localizer["the_new_parent_is_a_child_of_the_current_department"].Value }
                        };
                    }
                }

                var newHierarchyCode = GetNewHierarchyCode(command.ParentDepartment?.Id);
                var oldHierarchyCode = item.HierarchyCode;

                var subtree = _db.Departments
                    .Where(x => x.HierarchyCode.Length > oldHierarchyCode.Length) // go as deep starting from next level
                    .Where(x => x.HierarchyCode.StartsWith(oldHierarchyCode)) // get children
                    .ToList();

                // update the current department...
                item.HierarchyCode = newHierarchyCode;

                // ...and its children
                subtree.ForEach(x =>
                    x.HierarchyCode = newHierarchyCode + x.HierarchyCode.Substring(oldHierarchyCode.Length)
                );

                item.ParentDepartmentId = command.ParentDepartment?.Id;
            }
        }
        else
        {
            // validate duplication
            var exists = _db.Departments.FirstOrDefault(a => a.NameAr == command.NameAr
                                                             && a.ParentDepartmentId ==
                                                             (command.ParentDepartment != null
                                                                 ? command.ParentDepartment.Id
                                                                 : null));

            if (exists != null)
            {
                throw new GenericException
                {
                    Messages = new string[] { _localizer["0_is_already_exists_before"] }
                };
            }

            item = new Department
            {
                HierarchyCode = GetNewHierarchyCode(command.ParentDepartment?.Id)
            };
            _db.Departments.Add(item);
        }


        item.NameAr = command.NameAr.Trim();
        item.NameEn = command.NameEn?.Trim() ?? item.NameAr;
        item.Specializations = command.Specializations?.Trim();
        item.ParentDepartmentId = command.ParentDepartment?.Id;
        item.ManagerName = command.ManagerName?.Trim();
        item.ManagerEmail = command.ManagerEmail?.Trim().ToLower();
        item.MangerTitle = command.ManagerTitle?.Trim().ToLower();
        item.ManagerStaffNumber = command.ManagerStaffNumber?.Trim().ToLower();
        item.OrganizationTypeId = command.OrganizationType?.Id;
        item.Order = command.Order;
        item.IsMain = command.IsMain ? 1 : 0;
        item.IsPoliceStation = command.IsPoliceStation;
        item.LevelNumber = command.OrganizationType != null
            ? _db.OrganizationTypes
                .FirstOrDefault(x => x.Id == command.OrganizationType.Id)?.LevelNumber
            : null;

        _db.SaveChanges();


        // Return the newly created item using dtos.
        return Task.FromResult(
            _db
                .Departments
                .AsExpandable()
                .Select(DepartmentGetDto.Mapper(HelperFunctions.GetLanguageCode(), _db.Departments))
                .First(x => x.Id.Equals(item.Id))
        );
    }

    private string GetNewHierarchyCode(Guid? parentId)
    {
        // get the prefix of the parent:
        var parentPrefix = parentId == null
            ? ""
            : _db.Departments.First(x => x.Id.Equals(parentId)).HierarchyCode;

        // get the maximum current prefix
        var currentMaxPrefix = parentId == null
            ? _db.Departments
                  .Where(x => x.HierarchyCode.Length.Equals(Department
                      .HierarchyCodeLength)) // root level
                  .OrderByDescending(x => x.HierarchyCode)
                  .FirstOrDefault()?.HierarchyCode ??
              new string('-', Department.HierarchyCodeLength)
            : _db.Departments
                  .Where(x => x.HierarchyCode.Length.Equals(
                      parentPrefix.Length +
                      Department.HierarchyCodeLength)) // next immediate level
                  .Where(x => x.HierarchyCode.StartsWith(parentPrefix)) // children
                  .OrderByDescending(x => x.HierarchyCode) // max
                  .FirstOrDefault()?.HierarchyCode
                  ?.Last(Department.HierarchyCodeLength) ??
              new string('-', Department.HierarchyCodeLength);

        // create a new prefix
        var newPrefix =
            currentMaxPrefix.Equals(new string('-', Department.HierarchyCodeLength))
                ? parentPrefix + new string('0', Department.HierarchyCodeLength)
                : parentPrefix + HelperFunctions.Increment(currentMaxPrefix);

        return newPrefix;
    }
}
