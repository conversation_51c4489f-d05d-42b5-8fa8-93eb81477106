import { AbstractControl, ValidationErrors } from '@angular/forms';

/**
 * Validator that requires the control value to be an integer (no decimal places)
 * @param control The form control to validate
 * @returns ValidationErrors if the value is not an integer, null otherwise
 */
export function integerOnlyValidator(
    control: AbstractControl
): ValidationErrors | null {
    if (
        control.value === null ||
        control.value === undefined ||
        control.value === ''
    ) {
        return null; // Don't validate empty values, let required validator handle that
    }

    const value = control.value.toString();

    // Check if the value contains a decimal point
    if (value.includes('.')) {
        return { integerOnly: true };
    }

    // Check if the value is a valid integer
    const numericValue = Number(value);
    if (isNaN(numericValue) || !Number.isInteger(numericValue)) {
        return { integerOnly: true };
    }

    return null;
}
