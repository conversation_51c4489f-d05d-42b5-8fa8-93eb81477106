import { FlowItem } from '@injaz/features/flow/interfaces';
import { User } from '@injaz/common/models/user.model';
import { Item } from './item.model';

export interface NewUserRequest
    extends FlowItem<'submitted' | 'approved' | 'rejected'> {
    id: string;
    nameAr: string;
    nameEn: string;
    email: string;
    creationTime: Date;
    gender: string;
    employeeNumber: string;
    rank: string;
    requestedPermissions: string;
    department: Item;
    user: User;
    flowState: 'submitted' | 'approved' | 'rejected';
    password: string;
    passwordConfirmation: string;
}
