import { Injectable } from '@angular/core';
import { miscFunctions, Result } from 'mnm-webapp';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '@injaz/env/environment';
import { NewUserRequest } from '@injaz/common/models';

@Injectable()
export class NewUserRequestService {
    public constructor(private httpClient: HttpClient) {}

    public create(request: NewUserRequest): Observable<NewUserRequest> {
        const body = miscFunctions.objectToURLParams({
            request: JSON.stringify(request),
        });
        const headers = new HttpHeaders({
            // eslint-disable-next-line @typescript-eslint/naming-convention
            'Content-Type': 'application/x-www-form-urlencoded',
        });
        return this.httpClient
            .post<Result<NewUserRequest>>(
                environment.apiUrl + '/new-user-request',
                body.toString(),
                { headers: headers }
            )
            .pipe(map(result => result.extra));
    }
}
