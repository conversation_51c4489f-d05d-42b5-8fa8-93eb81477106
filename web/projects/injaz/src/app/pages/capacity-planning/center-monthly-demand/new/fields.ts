import { Validators } from '@angular/forms';
import {
    noWhitespaceValidator,
    integerOnlyValidator,
} from '@injaz/common/validators';
import { MnmForm<PERSON>ield } from '@injaz/shared/components';

export const fields: () => MnmFormField[] = () => [
    {
        name: 'id',
        hide: true,
    },
    {
        fields: [
            {
                name: 'department',
                type: 'select',
                label: 'translate_department',
                size: 6,
                validators: [Validators.required],
                bindLabel: 'name',
            },
            {
                name: 'time',
                type: 'select',
                label: 'translate_year_and_month',
                size: 6,
                validators: [Validators.required, integerOnlyValidator],
                bindLabel: 'name',
            },
        ],
    },
    {
        sectionTitle: 'translate_services_channels',
        fields: [
            {
                fields: [
                    {
                        name: 'happinessCenterVolume',
                        type: 'number',
                        label: 'translate_happiness_center_volume',
                        size: 3,
                        validators: [
                            Validators.required,
                            noWhitespaceValidator,
                            integerOnlyValidator,
                        ],
                        defaultValue: 0,
                        cssClass: 'text-center font-bold',
                    },
                    {
                        name: 'moiAppVolume',
                        type: 'number',
                        label: 'translate_moi_app_volume',
                        size: 3,
                        validators: [
                            Validators.required,
                            noWhitespaceValidator,
                            integerOnlyValidator,
                        ],
                        defaultValue: 0,
                        cssClass: 'text-center font-bold',
                    },
                    {
                        name: 'ajpAppVolume',
                        type: 'number',
                        label: 'translate_ajp_app_volume',
                        size: 3,
                        validators: [
                            Validators.required,
                            noWhitespaceValidator,
                            integerOnlyValidator,
                        ],
                        defaultValue: 0,
                        cssClass: 'text-center font-bold',
                    },
                    {
                        name: 'websiteVolume',
                        type: 'number',
                        label: 'translate_website_volume',
                        size: 3,
                        validators: [
                            Validators.required,
                            noWhitespaceValidator,
                            integerOnlyValidator,
                        ],
                        defaultValue: 0,
                        cssClass: 'text-center font-bold',
                    },
                ],
            },
        ],
    },
    {
        sectionTitle: 'translate_service_volumes',
        grids: 2,
        fields: [
            {
                name: 'fastServicesCount',
                type: 'number',
                label: 'translate_fast_services_count',
                size: 3,
                validators: [Validators.required, integerOnlyValidator],
                defaultValue: 0,
                cssClass: 'text-center font-bold',
            },
            {
                name: 'fastServiceTime',
                type: 'number',
                label: 'translate_fast_service_time',
                size: 3,
                validators: [Validators.required],
                defaultValue: 0,
                cssClass: 'text-center font-bold',
            },
            {
                name: 'regularServicesCount',
                type: 'number',
                label: 'translate_regular_services_count',
                size: 3,
                validators: [Validators.required, integerOnlyValidator],
                defaultValue: 0,
                cssClass: 'text-center font-bold',
            },
            {
                name: 'regularServiceTime',
                type: 'number',
                label: 'translate_regular_service_time',
                size: 3,
                validators: [Validators.required],
                defaultValue: 0,
                cssClass: 'text-center font-bold',
            },
            {
                name: 'complexServicesCount',
                type: 'number',
                label: 'translate_complex_services_count',
                size: 3,
                validators: [Validators.required, integerOnlyValidator],
                defaultValue: 0,
                cssClass: 'text-center font-bold',
            },
            {
                name: 'complexServiceTime',
                type: 'number',
                label: 'translate_complex_service_time',
                size: 3,
                validators: [Validators.required],
                defaultValue: 0,
                cssClass: 'text-center font-bold',
            },
        ],
    },
    {
        sectionTitle: 'translate_employee_and_work_config',
        grids: 2,
        fields: [
            {
                name: 'employeesAvailable',
                type: 'number',
                label: 'translate_employees_available',
                size: 4,
                validators: [Validators.required, integerOnlyValidator],
                defaultValue: 0,
                cssClass: 'text-center font-bold',
            },
            {
                name: 'dailyWorkHoursPerEmployee',
                type: 'number',
                label: 'translate_daily_work_hours_per_employee',
                size: 4,
                validators: [Validators.required],
                defaultValue: 0,
                cssClass: 'text-center font-bold',
            },
            {
                name: 'workDaysPerMonth',
                type: 'number',
                label: 'translate_work_days_per_month',
                size: 4,
                validators: [Validators.required],
                defaultValue: 0,
                cssClass: 'text-center font-bold',
            },
            {
                name: 'workHoursPerDay',
                type: 'number',
                label: 'translate_work_hours_per_day',
                size: 4,
                validators: [Validators.required],
                defaultValue: 0,
                cssClass: 'text-center font-bold',
            },
            {
                name: 'religiousAndNationalOccasionsDaysPerMonth',
                type: 'number',
                label: 'translate_religious_and_national_occasions_days_per_month',
                size: 4,
                validators: [Validators.required],
                defaultValue: 0,
                cssClass: 'text-center font-bold',
            },
        ],
    },
    {
        sectionTitle: 'translate_actual_usage',
        grids: 2,
        fields: [
            {
                name: 'actualTransactionsPerMonth',
                type: 'number',
                label: 'translate_actual_transactions_per_month',
                size: 4,
                validators: [Validators.required],
                defaultValue: 0,
                cssClass: 'text-center font-bold',
            },
            {
                name: 'actualCustomersPerMonth',
                type: 'number',
                label: 'translate_actual_customers_per_month',
                size: 4,
                validators: [Validators.required],
                defaultValue: 0,
                cssClass: 'text-center font-bold',
            },
        ],
    },
    {
        sectionTitle: 'translate_center_capacity',
        grids: 2,
        fields: [
            {
                name: 'availableSeatsInCenter',
                type: 'number',
                label: 'translate_available_seats_in_center',
                size: 6,
                validators: [Validators.required, ],
                defaultValue: 0,
                cssClass: 'text-center font-bold',
            },
            {
                name: 'availableParkingAtCenter',
                type: 'number',
                label: 'translate_available_parking_at_center',
                size: 6,
                validators: [Validators.required],
                defaultValue: 0,
                cssClass: 'text-center font-bold',
            },
        ],
    },
];
