import { Validators } from '@angular/forms';
import {
    noWhitespaceValidator,
    integerOnlyValidator,
} from '@injaz/common/validators';
import { MnmForm<PERSON>ield } from '@injaz/shared/components';

export const fields: () => MnmFormField[] = () => [
    {
        name: 'id',
        hide: true,
    },
    {
        fields: [
            {
                name: 'department',
                type: 'select',
                label: 'translate_department',
                size: 6,
                validators: [Validators.required],
                bindLabel: 'name',
            },
            {
                name: 'time',
                type: 'select',
                label: 'translate_time_dimension',
                size: 6,
                validators: [Validators.required],
                bindLabel: 'name',
            },
        ],
    },
    {
        sectionTitle: 'translate_service_volumes',
        grids: 2,
        fields: [
            {
                name: 'happinessCenterVolume',
                type: 'number',
                label: 'translate_happiness_center_volume',
                size: 3,
                validators: [Validators.required, noWhitespaceValidator],
                defaultValue: 0,
                cssClass: 'text-center font-bold',
            },
            {
                name: 'moiAppVolume',
                type: 'number',
                label: 'translate_moi_app_volume',
                size: 3,
                validators: [Validators.required, noWhitespaceValidator],
                defaultValue: 0,
                cssClass: 'text-center font-bold',
            },
            {
                name: 'ajpAppVolume',
                type: 'number',
                label: 'translate_ajp_app_volume',
                size: 3,
                validators: [Validators.required, noWhitespaceValidator],
                defaultValue: 0,
                cssClass: 'text-center font-bold',
            },
            {
                name: 'websiteVolume',
                type: 'number',
                label: 'translate_website_volume',
                size: 3,
                validators: [Validators.required, noWhitespaceValidator],
                defaultValue: 0,
                cssClass: 'text-center font-bold',
            },
        ],
    },
];
