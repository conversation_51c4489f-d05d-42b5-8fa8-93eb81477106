import { Validators } from '@angular/forms';
import {
    noWhitespaceValidator,
    integerOnlyValidator,
} from '@injaz/common/validators';
import { MnmFormField } from '@injaz/shared/components';

export const fields: () => MnmFormField[] = () => [
    {
        name: 'id',
        hide: true,
    },

    {
        fields: [
            {
                name: 'year',
                type: 'number',
                label: 'translate_year',
                maxValue: 2050,
                minValue: 2000,
                size: 4,
                validators: [Validators.required, noWhitespaceValidator],
            },
            {
                name: 'monthNumber',
                type: 'select',
                label: 'translate_month',
                size: 4,
                items: Array(12)
                    .fill(0)
                    .map((_, i) => i + 1),
                validators: [Validators.required],
                compareWith: (a, b) => a.id === b,
            },
        ],
    },
    {
        fields: [
            {
                name: 'autoAddMonths',
                type: 'checkbox',
                size: 12,
                label: 'translate_auto_generate_the_months',
            },
        ],
    },
];
