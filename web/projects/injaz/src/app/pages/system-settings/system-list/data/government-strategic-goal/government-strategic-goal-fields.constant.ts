import { Validators } from '@angular/forms';
import { integerOnlyValidator } from '@injaz/common/validators';
import { MnmFormField } from '@injaz/shared/components';

export const governmentStrategicGoalFields: MnmFormField[] = [
    {
        fields: [
            {
                name: 'id',
                hide: true,
            },
            {
                name: 'nameAr',
                type: 'text',
                label: 'translate_name_in_arabic',
                size: 6,
                validators: [
                    Validators.required,
                    Validators.maxLength(256),
                    Validators.minLength(3),
                ],
            },
            {
                name: 'nameEn',
                type: 'text',
                label: 'translate_name_in_english',
                size: 6,
                validators: [
                    Validators.maxLength(256),
                    Validators.minLength(3),
                ],
            },
        ],
    },
    {
        fields: [
            {
                name: 'fromYear',
                type: 'number',
                label: 'translate_from_year',
                size: 6,
                validators: [integerOnlyValidator],
            },

            {
                name: 'toYear',
                type: 'number',
                label: 'translate_to_year',
                size: 6,
                validators: [integerOnlyValidator],
            },
        ],
    },
];
