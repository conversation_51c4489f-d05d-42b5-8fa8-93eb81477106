import { Validators } from '@angular/forms';
import { integerOnlyValidator } from '@injaz/common/validators';
import { MnmForm<PERSON>ield } from '@injaz/shared/components';

export const kpiTypeFields: MnmFormField[] = [
    {
        fields: [
            {
                name: 'id',
                hide: true,
            },
            {
                name: 'nameAr',
                type: 'text',
                label: 'translate_name_in_arabic',
                size: 6,
                validators: [
                    Validators.required,
                    Validators.maxLength(256),
                    Validators.minLength(3),
                ],
            },
            {
                name: 'nameEn',
                type: 'text',
                label: 'translate_name_in_english',
                size: 6,
                validators: [
                    Validators.maxLength(256),
                    Validators.minLength(3),
                ],
            },
        ],
    },
    {
        fields: [
            {
                name: 'code',
                type: 'text',
                label: 'translate_code',
                size: 6,
                validators: [Validators.required],
            },
            {
                name: 'order',
                type: 'number',
                label: 'translate_order',
                size: 6,
            },
        ],
    },
    {
        fields: [
            {
                name: 'isUsedToComputeGoalAchievement',
                type: 'checkbox',
                label: 'translate_is_used_to_compute_goal_achievement',
                size: 4,
            },

            {
                name: 'isConsideredInDashboard',
                type: 'checkbox',
                label: 'translate_is_considered_in_dashboard',
                size: 4,
            },

            {
                name: 'IsExcludedFromCalculations',
                type: 'checkbox',
                label: 'translate_is_excluded_from_calculations',
                size: 4,
            },

            {
                name: 'isLinkableToPlans',
                type: 'checkbox',
                label: 'translate_is_linkable_to_plans',
                size: 4,
            },
        ],
    },
];
