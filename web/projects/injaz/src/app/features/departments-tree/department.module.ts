import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';
import { DepartmentSelectorComponent } from './department-selector.component';
import { DepartmentService } from './department.service';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
    declarations: [DepartmentSelectorComponent],
    imports: [
        CommonModule,
        ReactiveFormsModule,
        HttpClientModule,
        TranslateModule,
    ],
    providers: [DepartmentService],
    exports: [DepartmentSelectorComponent],
})
export class DepartmentModule {}
